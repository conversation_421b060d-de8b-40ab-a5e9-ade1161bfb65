import { StockStatus, Sale, PaymentSummary, PaymentMethod, DailySalesData } from '../types'

// Utility function to format Chilean pesos
export const formatCLP = (amount: number): string => {
  return new Intl.NumberFormat('es-CL', {
    style: 'currency',
    currency: 'CLP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Utility function to get stock status
export const getStockStatus = (stock: number): StockStatus => {
  if (stock === 0) return 'out-of-stock';
  if (stock <= 5) return 'critical';
  if (stock <= 10) return 'low';
  return 'normal';
}

// Utility function to calculate IVA
export const calculateIVA = (amount: number, rate: number = 0.19): number => {
  return amount * rate
}

// Utility function to calculate total with IVA
export const calculateTotalWithIVA = (amount: number, rate: number = 0.19): number => {
  return amount * (1 + rate)
}

// Cash Closing Utilities

// Generate payment summary from sales
export const generatePaymentSummary = (sales: Sale[]): PaymentSummary[] => {
  const paymentMethods: PaymentMethod[] = ['Efectivo', 'Cheque', 'Transferencia', 'Tarjeta']

  return paymentMethods.map(method => {
    const methodSales = sales.filter(sale => sale.paymentMethod === method)
    const amount = methodSales.reduce((total, sale) => total + sale.total, 0)

    return {
      paymentMethod: method,
      amount,
      transactionCount: methodSales.length
    }
  })
}

// Generate daily sales data from sales
export const generateDailySalesData = (
  sales: Sale[],
  cashier: string,
  shift: string,
  openingTime: string,
  closingTime: string
): DailySalesData => {
  const date = new Date().toLocaleDateString('es-CL')
  const totalReceipts = sales.filter(sale => sale.receiptType === 'boleta').length
  const totalInvoices = sales.filter(sale => sale.receiptType === 'factura').length
  const grossSales = sales.reduce((total, sale) => total + sale.total, 0)
  const totalDiscounts = sales.reduce((total, sale) => total + (sale.discountAmount || 0), 0)
  const totalReturns = 0 // TODO: Implement returns functionality
  const netSales = grossSales - totalDiscounts - totalReturns

  return {
    date,
    cashier,
    shift,
    openingTime,
    closingTime,
    totalReceipts,
    totalInvoices,
    grossSales,
    totalDiscounts,
    totalReturns,
    netSales
  }
}

// Format date and time for display
export const formatDateTime = (date: Date): string => {
  return new Intl.DateTimeFormat('es-CL', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(date)
}

// Format time only
export const formatTime = (date: Date): string => {
  return new Intl.DateTimeFormat('es-CL', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// Generate receipt number
export const generateReceiptNumber = (type: 'boleta' | 'factura'): string => {
  const prefix = type === 'boleta' ? 'B' : 'F'
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
  return `${prefix}${timestamp}${random}`
}

// Export and Print Utilities

// Generate CSV content for payment summary
export const generatePaymentSummaryCSV = (paymentSummary: PaymentSummary[]): string => {
  const headers = ['Medio de Pago', 'Monto', 'Transacciones']
  const rows = paymentSummary.map(payment => [
    payment.paymentMethod,
    payment.amount.toString(),
    payment.transactionCount.toString()
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.join(','))
    .join('\n')

  return csvContent
}

// Generate CSV content for daily sales data
export const generateDailySalesCSV = (dailySalesData: DailySalesData): string => {
  const data = [
    ['Campo', 'Valor'],
    ['Fecha', dailySalesData.date],
    ['Cajero', dailySalesData.cashier],
    ['Turno', dailySalesData.shift],
    ['Hora de apertura', dailySalesData.openingTime],
    ['Hora de cierre', dailySalesData.closingTime],
    ['Nº total boletas', dailySalesData.totalReceipts.toString()],
    ['Nº total facturas', dailySalesData.totalInvoices.toString()],
    ['Total ventas (bruto)', dailySalesData.grossSales.toString()],
    ['Total descuentos', dailySalesData.totalDiscounts.toString()],
    ['Total devoluciones', dailySalesData.totalReturns.toString()],
    ['Total ventas neto', dailySalesData.netSales.toString()]
  ]

  return data.map(row => row.join(',')).join('\n')
}

// Generate CSV content for sales detail
export const generateSalesDetailCSV = (sales: Sale[]): string => {
  const headers = [
    'Número Recibo',
    'Fecha y Hora',
    'Tipo',
    'Cliente',
    'Productos',
    'Subtotal',
    'IVA',
    'Total',
    'Método de Pago',
    'Código Descuento'
  ]

  const rows = sales.map(sale => [
    sale.receiptNumber,
    formatDateTime(sale.timestamp),
    sale.receiptType,
    sale.customer || '',
    sale.items.length.toString(),
    sale.subtotal.toString(),
    sale.iva.toString(),
    sale.total.toString(),
    sale.paymentMethod,
    sale.discountCode || ''
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')

  return csvContent
}

// Download CSV file
export const downloadCSV = (content: string, filename: string): void => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// Generate printable HTML content
export const generatePrintableHTML = (
  dailySalesData: DailySalesData,
  paymentSummary: PaymentSummary[],
  cashCount: { expectedCash: number; countedCash: number; difference: number; observations?: string },
  sales: Sale[]
): string => {
  const currentDate = new Date().toLocaleString('es-CL')

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Cierre de Caja - ${dailySalesData.date}</title>
      <style>
        @media print {
          body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
          .no-print { display: none !important; }
        }
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        .data-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px; }
        .data-item { display: flex; justify-content: space-between; padding: 5px 0; }
        .data-label { font-weight: bold; }
        .payment-summary { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        .payment-summary th, .payment-summary td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .payment-summary th { background-color: #f5f5f5; font-weight: bold; }
        .cash-count { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 15px; }
        .difference { font-weight: bold; font-size: 16px; }
        .difference.positive { color: #0066cc; }
        .difference.negative { color: #cc0000; }
        .difference.zero { color: #009900; }
        .sales-detail { font-size: 12px; }
        .sales-table { width: 100%; border-collapse: collapse; font-size: 11px; }
        .sales-table th, .sales-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .sales-table th { background-color: #f5f5f5; }
        .signatures { margin-top: 40px; display: grid; grid-template-columns: 1fr 1fr; gap: 50px; }
        .signature-box { text-align: center; border-top: 1px solid #333; padding-top: 10px; }
        .print-info { font-size: 10px; color: #666; text-align: center; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>ANDES SALUD</h1>
        <h2>REPORTE DE CIERRE DE CAJA</h2>
        <p>Fecha: ${dailySalesData.date} | Cajero: ${dailySalesData.cashier} | Turno: ${dailySalesData.shift}</p>
      </div>

      <div class="section">
        <div class="section-title">Datos del Cierre</div>
        <div class="data-grid">
          <div class="data-item">
            <span class="data-label">Hora de Apertura:</span>
            <span>${dailySalesData.openingTime}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Hora de Cierre:</span>
            <span>${dailySalesData.closingTime}</span>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Resumen de Ventas</div>
        <div class="data-grid">
          <div class="data-item">
            <span class="data-label">Nº total boletas:</span>
            <span>${dailySalesData.totalReceipts}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Nº total facturas:</span>
            <span>${dailySalesData.totalInvoices}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Total ventas (bruto):</span>
            <span>${formatCLP(dailySalesData.grossSales)}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Total descuentos:</span>
            <span>${formatCLP(dailySalesData.totalDiscounts)}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Total devoluciones:</span>
            <span>${formatCLP(dailySalesData.totalReturns)}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Total ventas neto:</span>
            <span><strong>${formatCLP(dailySalesData.netSales)}</strong></span>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Detalle por Medio de Pago</div>
        <table class="payment-summary">
          <thead>
            <tr>
              <th>Medio de Pago</th>
              <th>Monto</th>
              <th>Transacciones</th>
            </tr>
          </thead>
          <tbody>
            ${paymentSummary.map(payment => `
              <tr>
                <td>${payment.paymentMethod}</td>
                <td>${formatCLP(payment.amount)}</td>
                <td>${payment.transactionCount}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <div class="section">
        <div class="section-title">Arqueo de Caja</div>
        <div class="cash-count">
          <div class="data-item">
            <span class="data-label">Efectivo Esperado:</span>
            <span>${formatCLP(cashCount.expectedCash)}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Efectivo Contado:</span>
            <span>${formatCLP(cashCount.countedCash)}</span>
          </div>
          <div class="data-item">
            <span class="data-label">Diferencia:</span>
            <span class="difference ${cashCount.difference === 0 ? 'zero' : cashCount.difference > 0 ? 'positive' : 'negative'}">
              ${formatCLP(cashCount.difference)}
            </span>
          </div>
          ${cashCount.observations ? `
            <div style="margin-top: 10px;">
              <span class="data-label">Observaciones:</span>
              <p style="margin: 5px 0;">${cashCount.observations}</p>
            </div>
          ` : ''}
        </div>
      </div>

      ${sales.length > 0 ? `
        <div class="section sales-detail">
          <div class="section-title">Detalle de Ventas del Día</div>
          <table class="sales-table">
            <thead>
              <tr>
                <th>Recibo</th>
                <th>Hora</th>
                <th>Cliente</th>
                <th>Productos</th>
                <th>Total</th>
                <th>Pago</th>
              </tr>
            </thead>
            <tbody>
              ${sales.map(sale => `
                <tr>
                  <td>${sale.receiptNumber}</td>
                  <td>${formatTime(sale.timestamp)}</td>
                  <td>${sale.customer || '-'}</td>
                  <td>${sale.items.length}</td>
                  <td>${formatCLP(sale.total)}</td>
                  <td>${sale.paymentMethod}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      ` : ''}

      <div class="signatures">
        <div class="signature-box">
          <div>Firma Cajero</div>
        </div>
        <div class="signature-box">
          <div>Firma Supervisor</div>
        </div>
      </div>

      <div class="print-info">
        Reporte generado el ${currentDate} | Sistema POS Andes Salud
      </div>
    </body>
    </html>
  `
}

// Print HTML content
export const printHTML = (htmlContent: string): void => {
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
    printWindow.close()
  }
}
