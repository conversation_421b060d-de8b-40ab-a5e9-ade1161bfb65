export interface Product {
  id: string
  name: string
  price: number
  category: string
  stock: number
  prescription: boolean
  laboratory: string
  image: string
  sku: string
  barcode?: string
}

export interface CartItem extends Product {
  quantity: number
}

export type StockStatus = 'out-of-stock' | 'critical' | 'low' | 'normal'

export type PaymentMethod = 'Efectivo' | 'Cheque' | 'Transferencia' | 'Tarjeta'

export interface Category {
  name: string
  icon: any // Lucide icon component
}

// Cash Closing Types
export interface Sale {
  id: string
  timestamp: Date
  items: CartItem[]
  subtotal: number
  iva: number
  total: number
  paymentMethod: PaymentMethod
  customer?: string
  prescriptionFile?: string
  discountCode?: string
  discountAmount?: number
  receiptNumber: string
  receiptType: 'boleta' | 'factura'
}

export interface PaymentSummary {
  paymentMethod: PaymentMethod
  amount: number
  transactionCount: number
}

export interface CashCount {
  expectedCash: number
  countedCash: number
  difference: number
  observations?: string
}

export interface DailySalesData {
  date: string
  cashier: string
  shift: string
  openingTime: string
  closingTime: string
  totalReceipts: number
  totalInvoices: number
  grossSales: number
  totalDiscounts: number
  totalReturns: number
  netSales: number
}

export interface CashClosing {
  id: string
  date: string
  cashier: string
  shift: string
  openingTime: string
  closingTime: string
  paymentSummary: PaymentSummary[]
  cashCount: CashCount
  dailySalesData: DailySalesData
  sales: Sale[]
  cashierSignature?: string
  supervisorSignature?: string
  status: 'open' | 'closed'
  createdAt: Date
  closedAt?: Date
}
