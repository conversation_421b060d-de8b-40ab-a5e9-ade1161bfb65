"use client"

import { useState, useCallback } from 'react'
import { Product, CartItem } from '../types'

export function useCart(products: Product[]) {
  const [cart, setCart] = useState<CartItem[]>([])

  // Get available stock for a product (original stock minus quantity in cart)
  const getAvailableStock = useCallback((productId: string) => {
    const originalProduct = products.find(p => p.id === productId)
    const cartItem = cart.find(item => item.id === productId)
    const quantityInCart = cartItem ? cartItem.quantity : 0

    if (!originalProduct) return 0
    return Math.max(0, originalProduct.stock - quantityInCart)
  }, [products, cart])

  // Add product to cart
  const addToCart = useCallback((product: Product) => {
    const existing = cart.find((item) => item.id === product.id)
    const currentQuantityInCart = existing ? existing.quantity : 0

    // Get original product to check stock
    const originalProduct = products.find(p => p.id === product.id)
    if (!originalProduct) return

    // Check if there's enough stock available
    if (currentQuantityInCart >= originalProduct.stock) {
      return // Don't add if already reached maximum original stock
    }

    setCart((prev) => {
      if (existing) {
        return prev.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      }
      return [...prev, { ...product, quantity: 1 }]
    })
  }, [cart, products])

  // Update quantity of item in cart
  const updateQuantity = useCallback((id: string, change: number) => {
    const cartItem = cart.find(item => item.id === id)
    if (!cartItem) return

    const originalProduct = products.find(p => p.id === id)
    if (!originalProduct) return

    const newQuantity = cartItem.quantity + change

    // If incrementing, check that it doesn't exceed original stock
    if (change > 0 && newQuantity > originalProduct.stock) {
      return // Don't allow increment if it exceeds original stock
    }

    // If new quantity is 0 or less, remove from cart
    if (newQuantity <= 0) {
      setCart((prev) => prev.filter((item) => item.id !== id))
      return
    }

    setCart((prev) =>
      prev.map((item) =>
        item.id === id
          ? { ...item, quantity: newQuantity }
          : item
      )
    )
  }, [cart, products])

  // Remove item from cart completely
  const removeFromCart = useCallback((id: string) => {
    setCart((prev) => prev.filter((item) => item.id !== id))
  }, [])

  // Clear entire cart
  const clearCart = useCallback(() => {
    setCart([])
  }, [])

  // Get total number of items in cart
  const getTotalItems = useCallback(() => {
    return cart.reduce((total, item) => total + item.quantity, 0)
  }, [cart])

  // Get subtotal (before tax)
  const getSubtotal = useCallback(() => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  }, [cart])

  // Get products with updated stock (showing available stock)
  const getProductsWithUpdatedStock = useCallback(() => {
    return products.map(product => ({
      ...product,
      stock: getAvailableStock(product.id)
    }))
  }, [products, getAvailableStock])

  return {
    cart,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalItems,
    getSubtotal,
    getAvailableStock,
    getProductsWithUpdatedStock
  }
}
