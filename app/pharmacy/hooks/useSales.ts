"use client"

import { useState, useCallback } from 'react'
import { Sale, CartItem, PaymentMethod } from '../types'
import { generateReceiptNumber, calculateIVA, calculateTotalWithIVA } from '../utils'

export function useSales() {
  const [sales, setSales] = useState<Sale[]>([])

  // Add a new sale
  const addSale = useCallback((
    items: CartItem[],
    paymentMethod: PaymentMethod,
    customer?: string,
    prescriptionFile?: string,
    discountCode?: string,
    discountAmount?: number
  ) => {
    const subtotal = items.reduce((total, item) => total + (item.price * item.quantity), 0)
    const iva = calculateIVA(subtotal)
    const total = calculateTotalWithIVA(subtotal) - (discountAmount || 0)
    
    const newSale: Sale = {
      id: `sale_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      items: [...items],
      subtotal,
      iva,
      total,
      paymentMethod,
      customer,
      prescriptionFile,
      discountCode,
      discountAmount,
      receiptNumber: generateReceiptNumber('boleta'), // Default to boleta
      receiptType: 'boleta'
    }

    setSales(prev => [...prev, newSale])
    return newSale
  }, [])

  // Get sales for today
  const getTodaysSales = useCallback(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    return sales.filter(sale => 
      sale.timestamp >= today && sale.timestamp < tomorrow
    )
  }, [sales])

  // Get total sales amount for today
  const getTodaysTotalSales = useCallback(() => {
    const todaysSales = getTodaysSales()
    return todaysSales.reduce((total, sale) => total + sale.total, 0)
  }, [getTodaysSales])

  // Get sales count for today
  const getTodaysSalesCount = useCallback(() => {
    return getTodaysSales().length
  }, [getTodaysSales])

  // Get sales by payment method for today
  const getTodaysSalesByPaymentMethod = useCallback((method: PaymentMethod) => {
    const todaysSales = getTodaysSales()
    return todaysSales.filter(sale => sale.paymentMethod === method)
  }, [getTodaysSales])

  // Clear all sales (for testing or new day)
  const clearSales = useCallback(() => {
    setSales([])
  }, [])

  // Get all sales
  const getAllSales = useCallback(() => {
    return sales
  }, [sales])

  return {
    sales,
    addSale,
    getTodaysSales,
    getTodaysTotalSales,
    getTodaysSalesCount,
    getTodaysSalesByPaymentMethod,
    clearSales,
    getAllSales
  }
}
