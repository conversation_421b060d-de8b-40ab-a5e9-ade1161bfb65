"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { ShoppingCart, Plus, Minus, Trash2 } from "lucide-react"
import { CartItem } from '../types'
import { formatCLP, calculateIVA, calculateTotalWithIVA } from '../utils'

interface CartProps {
  cart: CartItem[]
  onUpdateQuantity: (id: string, change: number) => void
  onRemoveItem: (id: string) => void
  onOpenPayment: () => void
}

export default function Cart({ 
  cart, 
  onUpdateQuantity, 
  onRemoveItem, 
  onOpenPayment 
}: CartProps) {
  
  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0)
  }

  const getSubtotal = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const subtotal = getSubtotal()
  const iva = calculateIVA(subtotal)
  const total = calculateTotalWithIVA(subtotal)

  return (
    <div className="space-y-4 sticky top-4 h-fit">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl">
            <ShoppingCart className="h-6 w-6" />
            Carrito ({getTotalItems()})
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-64">
            <div className="p-4 space-y-3 min-h-full">
              {cart.length === 0 ? (
                <p className="text-center text-gray-500 py-8">Carrito vacío</p>
              ) : (
                cart.map((item) => (
                  <div key={item.id} className="flex items-center gap-3 p-3 bg-secondary/30 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm leading-tight">{item.name}</h4>
                      <p className="text-primary font-semibold">{formatCLP(item.price)}</p>
                      <p className="text-xs text-gray-600">{item.laboratory}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0 rounded-full"
                        onClick={() => onUpdateQuantity(item.id, -1)}
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                      <span className="font-semibold min-w-[2rem] text-center">{item.quantity}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 w-8 p-0 rounded-full"
                        onClick={() => onUpdateQuantity(item.id, 1)}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        className="h-8 w-8 p-0 rounded-full ml-2"
                        onClick={() => onRemoveItem(item.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Resumen de Totales */}
      <Card>
        <CardContent className="p-4 space-y-3">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>{formatCLP(subtotal)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>IVA (19%):</span>
            <span>{formatCLP(iva)}</span>
          </div>
          <Separator />
          <div className="flex justify-between font-bold text-lg">
            <span>Total:</span>
            <span className="text-primary">{formatCLP(total)}</span>
          </div>
          <Button
            className="w-full h-12 text-lg font-semibold rounded-full cursor-pointer"
            disabled={cart.length === 0}
            onClick={onOpenPayment}
          >
            Pagar
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
