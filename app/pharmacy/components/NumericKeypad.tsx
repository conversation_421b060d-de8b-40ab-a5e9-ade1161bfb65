import React, { useEffect } from 'react';
import { X, Delete } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface NumericKeypadProps {
  isOpen: boolean;
  onClose: () => void;
  value: string;
  onValueChange: (value: string) => void;
  title?: string;
  maxLength?: number;
  onConfirm?: (value: string) => void;
}

const NumericKeypad: React.FC<NumericKeypadProps> = ({
  isOpen,
  onClose,
  value,
  onValueChange,
  title = "Ingrese el monto",
  maxLength = 10,
  onConfirm
}) => {
  const handleNumberClick = (num: string) => {
    if (value.length >= maxLength) return;
    onValueChange(value + num);
  };

  // Handle keyboard events
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent default behavior for handled keys
      const handledKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'Backspace', 'Delete', 'Enter', 'Escape'];
      if (handledKeys.includes(event.key)) {
        event.preventDefault();
      }

      // Handle number keys
      if (event.key >= '0' && event.key <= '9') {
        handleNumberClick(event.key);
      }
      // Handle backspace/delete
      else if (event.key === 'Backspace' || event.key === 'Delete') {
        handleDelete();
      }
      // Handle enter to confirm
      else if (event.key === 'Enter') {
        handleConfirm();
      }
      // Handle escape to close
      else if (event.key === 'Escape') {
        onClose();
      }
      // Handle 'c' or 'C' to clear
      else if (event.key.toLowerCase() === 'c') {
        handleClear();
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, value, maxLength, onValueChange, onClose, onConfirm]);

  const handleDelete = () => {
    onValueChange(value.slice(0, -1));
  };

  const handleClear = () => {
    onValueChange('');
  };

  const handleConfirm = () => {
    if (onConfirm && value) {
      onConfirm(value);
    }
    onClose();
  };

const formatDisplayValue = (val: string) => {
  if (!val) return '0';
  // Usar puntos como separadores de miles (formato CLP)
  return val.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
};

const keypadButtons = [
  ['1', '2', '3'],
  ['4', '5', '6'],
  ['7', '8', '9'],
  ['C', '0', '⌫']
];

return (
  <Dialog open={isOpen} onOpenChange={onClose}>
    <DialogContent className="max-w-sm mx-auto p-0 bg-white rounded-3xl shadow-2xl border-0 [&>button]:hidden">
      <DialogHeader className="p-6 pb-4 relative">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
        >
          <X size={16} className="text-gray-600" />
        </button>
        <DialogTitle className="text-xl font-semibold text-gray-800 text-center">
          {title}
        </DialogTitle>
        <p className="text-sm text-gray-500 text-center mt-2">
          Use el teclado numérico o haga clic en los botones
        </p>
      </DialogHeader>
      
      <div className="px-6 pb-6">
        {/* Display Value */}
        <div className="bg-gray-50 rounded-2xl p-4 mb-6 min-h-[60px] flex items-center justify-center">
          <span className="text-3xl font-bold text-[#003CA6] tracking-wide">
            ${formatDisplayValue(value)}
          </span>
        </div>
  {/* Keypad Grid - Centrado */}
  <div className="flex justify-center mb-6">
            <div className="grid grid-cols-3 gap-8 max-w-[280px]">
              {keypadButtons.flat().map((button, index) => {
                const isSpecial = ['C', '⌫'].includes(button);
                
                return (
                  <button
                    key={index}
                    onClick={() => {
                      if (button === 'C') handleClear();
                      else if (button === '⌫') handleDelete();
                      else handleNumberClick(button);
                    }}
                    className={`
                      w-18 h-18 rounded-full font-bold text-xl transition-all duration-200
                      transform active:scale-95 hover:scale-105 shadow-lg
                      flex flex-col items-center justify-center
                      ${isSpecial
                        ? 'bg-[#00D9F9] hover:bg-[#00C4E6] text-white'
                        : 'bg-[#003CA6] hover:bg-[#002B7A] text-white'
                      }
                      focus:outline-none focus:ring-4 focus:ring-[#00D9F9]/30
                    `}
                    title={
                      button === 'C' ? 'Limpiar (Tecla C)' :
                      button === '⌫' ? 'Borrar (Backspace/Delete)' :
                      `Número ${button}`
                    }
                  >
                    {button === '⌫' ? (
                      <>
                        <Delete size={20} className="flex-shrink-0" />
                        <span className="text-xs opacity-70 mt-0.5">DEL</span>
                      </>
                    ) : button === 'C' ? (
                      <>
                        <span className="flex-shrink-0">{button}</span>
                        <span className="text-xs opacity-70 mt-0.5">C</span>
                      </>
                    ) : (
                      <span className="flex-shrink-0">{button}</span>
                    )}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1 h-12 rounded-xl border-2 border-[#003CA6] text-[#003CA6] hover:bg-[#003CA6] hover:text-white transition-colors"
            >
              <div className="flex flex-col items-center">
                <span>Cancelar</span>
                <span className="text-xs opacity-70">ESC</span>
              </div>
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!value}
              className="flex-1 h-12 rounded-xl bg-[#00D9F9] hover:bg-[#00C4E6] text-[#003CA6] font-semibold disabled:bg-gray-300 disabled:text-gray-500"
            >
              <div className="flex flex-col items-center">
                <span>Confirmar</span>
                <span className="text-xs opacity-70">ENTER</span>
              </div>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NumericKeypad;
         