"use client"

import React, { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Plus, Minus, Calculator } from "lucide-react"
import NumericKeypad from './NumericKeypad'

interface QuantitySelectorProps {
  quantity: number
  maxQuantity: number
  onQuantityChange: (newQuantity: number) => void
  size?: 'sm' | 'md' | 'lg'
  showKeypad?: boolean
}

const QuantitySelector: React.FC<QuantitySelectorProps> = ({
  quantity,
  maxQuantity,
  onQuantityChange,
  size = 'sm',
  showKeypad = false
}) => {
  const [showNumericKeypad, setShowNumericKeypad] = useState(false)
  const [keypadValue, setKeypadValue] = useState('')

  const handleIncrement = () => {
    if (quantity < maxQuantity) {
      onQuantityChange(quantity + 1)
    }
  }

  const handleDecrement = () => {
    if (quantity > 1) {
      onQuantityChange(quantity - 1)
    }
  }

  const handleKeypadOpen = () => {
    setKeypadValue(quantity.toString())
    setShowNumericKeypad(true)
  }

  const handleKeypadConfirm = (value: string) => {
    const newQuantity = parseInt(value) || 1
    const finalQuantity = Math.min(Math.max(1, newQuantity), maxQuantity)
    onQuantityChange(finalQuantity)
    setShowNumericKeypad(false)
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'lg':
        return {
          button: 'h-10 w-10 p-0',
          icon: 'h-4 w-4',
          text: 'text-lg font-semibold min-w-[3rem]'
        }
      case 'md':
        return {
          button: 'h-9 w-9 p-0',
          icon: 'h-3.5 w-3.5',
          text: 'text-base font-semibold min-w-[2.5rem]'
        }
      default: // sm
        return {
          button: 'h-8 w-8 p-0',
          icon: 'h-3 w-3',
          text: 'text-sm font-semibold min-w-[2rem]'
        }
    }
  }

  const sizeClasses = getSizeClasses()

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          size="sm"
          variant="outline"
          className={`${sizeClasses.button} rounded-full`}
          onClick={handleDecrement}
          disabled={quantity <= 1}
        >
          <Minus className={sizeClasses.icon} />
        </Button>
        
        {showKeypad ? (
          <Button
            variant="ghost"
            className={`${sizeClasses.text} text-center hover:bg-gray-100 rounded-lg px-2`}
            onClick={handleKeypadOpen}
          >
            {quantity}
          </Button>
        ) : (
          <span className={`${sizeClasses.text} text-center`}>
            {quantity}
          </span>
        )}
        
        <Button
          size="sm"
          variant="outline"
          className={`${sizeClasses.button} rounded-full`}
          onClick={handleIncrement}
          disabled={quantity >= maxQuantity}
        >
          <Plus className={sizeClasses.icon} />
        </Button>

        {showKeypad && (
          <Button
            size="sm"
            variant="outline"
            className={`${sizeClasses.button} rounded-full ml-1 border-[#003CA6] text-[#003CA6] hover:bg-[#003CA6] hover:text-white`}
            onClick={handleKeypadOpen}
          >
            <Calculator className={sizeClasses.icon} />
          </Button>
        )}
      </div>

      {/* Numeric Keypad */}
      <NumericKeypad
        isOpen={showNumericKeypad}
        onClose={() => setShowNumericKeypad(false)}
        value={keypadValue}
        onValueChange={setKeypadValue}
        title="Ingrese la cantidad"
        maxLength={3}
        onConfirm={handleKeypadConfirm}
      />
    </>
  )
}

export default QuantitySelector
