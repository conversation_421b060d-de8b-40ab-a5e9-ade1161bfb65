"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { Printer, X, Eye } from "lucide-react"

interface PrintPreviewModalProps {
  isOpen: boolean
  onClose: () => void
  htmlContent: string
  onPrint: () => void
}

export default function PrintPreviewModal({ 
  isOpen, 
  onClose, 
  htmlContent,
  onPrint 
}: PrintPreviewModalProps) {

  const handlePrint = () => {
    onPrint()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Vista Previa del Reporte
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {/* Preview Content */}
          <div className="flex-1 overflow-auto border rounded-lg bg-white">
            <div 
              className="p-4 min-h-full"
              dangerouslySetInnerHTML={{ __html: htmlContent }}
              style={{
                fontFamily: 'Arial, sans-serif',
                fontSize: '14px',
                lineHeight: '1.4'
              }}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-600">
              Vista previa del reporte de cierre de caja
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={onClose}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Cancelar
              </Button>
              <Button 
                onClick={handlePrint}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                Imprimir
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
