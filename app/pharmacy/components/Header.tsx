"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Clock, Calculator } from "lucide-react"

// Componente del reloj que se renderiza solo del lado del cliente
function ClockDisplay() {
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    setCurrentTime(new Date())

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  if (!mounted || !currentTime) {
    return <span className="font-mono w-16 text-center">--:--:--</span>
  }

  return (
    <span className="font-mono w-16 text-center">
      {currentTime.toLocaleTimeString('es-CL', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })}
    </span>
  )
}

interface HeaderProps {
  onOpenCashClosing?: () => void
}

export default function Header({ onOpenCashClosing }: HeaderProps) {
  return (
    <header className="bg-gradient-to-r from-primary to-primary/90 text-white shadow-lg">
      <div className="max-w-7xl mx-auto">
        <div className="flex items-center justify-between p-4">
          {/* Logo y Título */}
          <div className="flex items-center gap-4">
            <div className="relative w-12 h-12">
              <Image
                src="/logo/collapse-logo.png"
                alt="Andes Salud"
                fill
                className="object-contain"
                sizes="48px"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Andes Salud</h1>
              <p className="text-sm text-blue-100">Sistema POS Farmacia</p>
            </div>
          </div>

          {/* Botones de Acción, Usuario y Reloj */}
          <div className="flex items-center gap-6">
            {/* Botón Cierre de Caja */}
            {onOpenCashClosing && (
              <Button
                variant="outline"
                size="sm"
                onClick={onOpenCashClosing}
                className="bg-white/10 border-white/20 text-white hover:bg-white/20 hover:text-white rounded-full"
              >
                <Calculator className="h-4 w-4 mr-2" />
                Cierre de Caja
              </Button>
            )}

            {/* Reloj */}
            <div className="flex items-center gap-2 text-blue-100">
              <Clock className="h-4 w-4" />
              <ClockDisplay />
            </div>

            {/* Avatar Usuario */}
            <div className="flex items-center gap-2">
              <div className="relative w-8 h-8 rounded-full overflow-hidden bg-blue-100">
                <Image
                  src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=100&h=100&fit=crop&crop=face"
                  alt="Farmacéutico"
                  fill
                  className="object-cover"
                  sizes="32px"
                />
              </div>
              <div className="hidden sm:block">
                <p className="text-sm font-medium">Farmacéutico</p>
                <p className="text-xs text-blue-100">En línea</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
