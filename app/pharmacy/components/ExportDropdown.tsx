"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Download, 
  FileText, 
  DollarSign, 
  Calculator, 
  Receipt,
  ChevronDown,
  ChevronUp
} from "lucide-react"

interface ExportOption {
  id: string
  label: string
  description: string
  icon: any
  onClick: () => void
  disabled?: boolean
}

interface ExportDropdownProps {
  options: ExportOption[]
}

export default function ExportDropdown({ options }: ExportDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="relative">
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2"
      >
        <Download className="h-4 w-4" />
        Exportar Datos
        {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
      </Button>

      {isOpen && (
        <Card className="absolute top-full left-0 mt-2 w-64 z-50 shadow-lg">
          <CardContent className="p-2">
            <div className="space-y-1">
              {options.map((option) => {
                const IconComponent = option.icon
                return (
                  <Button
                    key={option.id}
                    variant="ghost"
                    size="sm"
                    disabled={option.disabled}
                    onClick={() => {
                      option.onClick()
                      setIsOpen(false)
                    }}
                    className="w-full justify-start gap-2 h-auto p-3"
                  >
                    <IconComponent className="h-4 w-4 flex-shrink-0" />
                    <div className="text-left">
                      <div className="font-medium text-sm">{option.label}</div>
                      <div className="text-xs text-gray-500">{option.description}</div>
                    </div>
                  </Button>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
