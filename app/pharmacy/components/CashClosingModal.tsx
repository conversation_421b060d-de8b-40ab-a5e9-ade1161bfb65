"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import {
  Calculator,
  FileText,
  Clock,
  User,
  DollarSign,
  Receipt,
  Eye,
  Printer,
  Download,
  X
} from "lucide-react"
import ExportDropdown from './ExportDropdown'
import PrintPreviewModal from './PrintPreviewModal'
import Toast, { useToast } from './Toast'
import { Sale, PaymentSummary, CashCount, DailySalesData } from '../types'
import {
  formatCLP,
  generatePaymentSummary,
  generateDailySalesData,
  formatDateTime,
  generatePaymentSummaryCSV,
  generateDailySalesCSV,
  generateSalesDetailCSV,
  downloadCSV,
  generatePrintableHTML,
  printHTML
} from '../utils'

interface CashClosingModalProps {
  isOpen: boolean
  onClose: () => void
  sales: Sale[]
}

export default function CashClosingModal({ 
  isOpen, 
  onClose, 
  sales 
}: CashClosingModalProps) {
  
  // Form states
  const [cashier, setCashier] = useState("Juan Pérez")
  const [shift, setShift] = useState("Tarde")
  const [openingTime, setOpeningTime] = useState("08:30")
  const [closingTime, setClosingTime] = useState("18:00")
  const [expectedCash, setExpectedCash] = useState("")
  const [countedCash, setCountedCash] = useState("")
  const [observations, setObservations] = useState("")
  const [showDailySales, setShowDailySales] = useState(false)
  const [showPrintPreview, setShowPrintPreview] = useState(false)
  const [printContent, setPrintContent] = useState("")

  // Toast notifications
  const { toast, showToast, hideToast } = useToast()

  // Calculate payment summary
  const paymentSummary = generatePaymentSummary(sales)
  
  // Calculate cash count
  const expectedCashAmount = parseFloat(expectedCash) || 0
  const countedCashAmount = parseFloat(countedCash) || 0
  const cashDifference = countedCashAmount - expectedCashAmount

  // Generate daily sales data
  const dailySalesData = generateDailySalesData(
    sales, 
    cashier, 
    shift, 
    openingTime, 
    closingTime
  )

  // Print and Export Functions
  const handlePrintPreview = () => {
    const cashCount = {
      expectedCash: expectedCashAmount,
      countedCash: countedCashAmount,
      difference: cashDifference,
      observations
    }

    const htmlContent = generatePrintableHTML(
      dailySalesData,
      paymentSummary,
      cashCount,
      sales
    )

    setPrintContent(htmlContent)
    setShowPrintPreview(true)
  }

  const handlePrint = () => {
    printHTML(printContent)
  }

  const handleExportPaymentSummary = () => {
    const csvContent = generatePaymentSummaryCSV(paymentSummary)
    const filename = `medios-de-pago-${dailySalesData.date.replace(/\//g, '-')}.csv`
    downloadCSV(csvContent, filename)
    showToast('Resumen de medios de pago exportado exitosamente', 'success')
  }

  const handleExportDailySales = () => {
    const csvContent = generateDailySalesCSV(dailySalesData)
    const filename = `cierre-de-caja-${dailySalesData.date.replace(/\//g, '-')}.csv`
    downloadCSV(csvContent, filename)
    showToast('Datos de cierre de caja exportados exitosamente', 'success')
  }

  const handleExportSalesDetail = () => {
    const csvContent = generateSalesDetailCSV(sales)
    const filename = `detalle-ventas-${dailySalesData.date.replace(/\//g, '-')}.csv`
    downloadCSV(csvContent, filename)
    showToast('Detalle de ventas exportado exitosamente', 'success')
  }

  const handleExportArqueo = () => {
    const arqueoData = [
      ['Concepto', 'Monto'],
      ['Efectivo esperado', expectedCashAmount.toString()],
      ['Efectivo contado', countedCashAmount.toString()],
      ['Diferencia', cashDifference.toString()],
      ['Observaciones', observations || '']
    ]

    const csvContent = arqueoData.map(row => row.join(',')).join('\n')
    const filename = `arqueo-de-caja-${dailySalesData.date.replace(/\//g, '-')}.csv`
    downloadCSV(csvContent, filename)
    showToast('Arqueo de caja exportado exitosamente', 'success')
  }

  const handleCloseCash = () => {
    // TODO: Implement cash closing logic
    console.log("Closing cash register...")
    onClose()
  }

  // Export options for dropdown
  const exportOptions = [
    {
      id: 'daily-sales',
      label: 'Cierre de Caja Completo',
      description: 'Datos generales del cierre',
      icon: FileText,
      onClick: handleExportDailySales
    },
    {
      id: 'payment-summary',
      label: 'Resumen de Medios de Pago',
      description: 'Totales por método de pago',
      icon: DollarSign,
      onClick: handleExportPaymentSummary
    },
    {
      id: 'cash-count',
      label: 'Arqueo de Caja',
      description: 'Conteo de efectivo y diferencias',
      icon: Calculator,
      onClick: handleExportArqueo
    },
    {
      id: 'sales-detail',
      label: 'Detalle de Ventas',
      description: 'Lista completa de transacciones',
      icon: Receipt,
      onClick: handleExportSalesDetail,
      disabled: sales.length === 0
    }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Calculator className="h-6 w-6" />
            Cierre de Caja
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Datos del Cierre */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Datos del Cierre
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="date">Fecha</Label>
                <Input 
                  id="date" 
                  value={new Date().toLocaleDateString('es-CL')} 
                  disabled 
                />
              </div>
              <div>
                <Label htmlFor="cashier">Cajero</Label>
                <Input 
                  id="cashier" 
                  value={cashier} 
                  onChange={(e) => setCashier(e.target.value)} 
                />
              </div>
              <div>
                <Label htmlFor="shift">Turno</Label>
                <Input 
                  id="shift" 
                  value={shift} 
                  onChange={(e) => setShift(e.target.value)} 
                />
              </div>
              <div>
                <Label htmlFor="opening-time">Hora de Apertura</Label>
                <Input 
                  id="opening-time" 
                  type="time"
                  value={openingTime} 
                  onChange={(e) => setOpeningTime(e.target.value)} 
                />
              </div>
              <div>
                <Label htmlFor="closing-time">Hora de Cierre</Label>
                <Input 
                  id="closing-time" 
                  type="time"
                  value={closingTime} 
                  onChange={(e) => setClosingTime(e.target.value)} 
                />
              </div>
            </CardContent>
          </Card>

          {/* Resumen de Ventas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Resumen de Ventas
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Nº total boletas:</span>
                  <span className="font-semibold">{dailySalesData.totalReceipts}</span>
                </div>
                <div className="flex justify-between">
                  <span>Nº total facturas:</span>
                  <span className="font-semibold">{dailySalesData.totalInvoices}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total ventas (bruto):</span>
                  <span className="font-semibold">{formatCLP(dailySalesData.grossSales)}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total descuentos:</span>
                  <span className="font-semibold">{formatCLP(dailySalesData.totalDiscounts)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total devoluciones:</span>
                  <span className="font-semibold">{formatCLP(dailySalesData.totalReturns)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold text-primary">
                  <span>Total ventas neto:</span>
                  <span>{formatCLP(dailySalesData.netSales)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Medios de Pago */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Detalle por Medio de Pago
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {paymentSummary.map((payment) => (
                  <div key={payment.paymentMethod} className="flex justify-between items-center p-3 bg-secondary/30 rounded-lg">
                    <div>
                      <span className="font-medium">{payment.paymentMethod}</span>
                      <span className="text-sm text-gray-600 ml-2">
                        ({payment.transactionCount} transacciones)
                      </span>
                    </div>
                    <span className="font-bold text-lg">{formatCLP(payment.amount)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Arqueo de Caja */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Arqueo de Caja
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="expected-cash">Efectivo Esperado</Label>
                  <Input 
                    id="expected-cash" 
                    type="number"
                    placeholder="0"
                    value={expectedCash}
                    onChange={(e) => setExpectedCash(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="counted-cash">Efectivo Contado</Label>
                  <Input 
                    id="counted-cash" 
                    type="number"
                    placeholder="0"
                    value={countedCash}
                    onChange={(e) => setCountedCash(e.target.value)}
                  />
                </div>
                <div>
                  <Label>Diferencia</Label>
                  <div className={`p-2 rounded border text-center font-bold ${
                    cashDifference === 0 ? 'bg-green-50 text-green-700' :
                    cashDifference > 0 ? 'bg-blue-50 text-blue-700' :
                    'bg-red-50 text-red-700'
                  }`}>
                    {formatCLP(cashDifference)}
                  </div>
                </div>
              </div>
              <div>
                <Label htmlFor="observations">Observaciones</Label>
                <Textarea 
                  id="observations"
                  placeholder="Ingrese observaciones sobre el arqueo de caja..."
                  value={observations}
                  onChange={(e) => setObservations(e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Botones de Acción */}
          <div className="space-y-4">
            {/* Primera fila - Ver detalle */}
            <div className="flex justify-center">
              <Button
                variant="outline"
                onClick={() => setShowDailySales(!showDailySales)}
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                {showDailySales ? 'Ocultar' : 'Ver'} Detalle Venta Diaria
              </Button>
            </div>

            {/* Segunda fila - Acciones principales */}
            <div className="flex justify-between items-center">
              <div className="flex gap-2">
                <ExportDropdown options={exportOptions} />
                <Button
                  variant="outline"
                  onClick={handlePrintPreview}
                  className="flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  Imprimir Reporte
                </Button>
              </div>
              <Button
                onClick={handleCloseCash}
                className="flex items-center gap-2"
              >
                <Calculator className="h-4 w-4" />
                Cerrar Caja
              </Button>
            </div>
          </div>

          {/* Detalle de Ventas Diarias */}
          {showDailySales && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Detalle de Ventas del Día
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {sales.length === 0 ? (
                    <p className="text-center text-gray-500 py-4">No hay ventas registradas</p>
                  ) : (
                    sales.map((sale) => (
                      <div key={sale.id} className="p-3 border rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{sale.receiptNumber}</p>
                            <p className="text-sm text-gray-600">
                              {formatDateTime(sale.timestamp)}
                            </p>
                            <p className="text-sm text-gray-600">
                              {sale.items.length} productos - {sale.paymentMethod}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold">{formatCLP(sale.total)}</p>
                            {sale.customer && (
                              <p className="text-sm text-gray-600">{sale.customer}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>

      {/* Print Preview Modal */}
      <PrintPreviewModal
        isOpen={showPrintPreview}
        onClose={() => setShowPrintPreview(false)}
        htmlContent={printContent}
        onPrint={handlePrint}
      />

      {/* Toast Notifications */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </Dialog>
  )
}
