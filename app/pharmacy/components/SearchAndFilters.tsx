"use client"

import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, User, Paperclip, Tag } from "lucide-react"
import { Category } from '../types'

interface SearchAndFiltersProps {
  searchTerm: string
  onSearchChange: (value: string) => void
  selectedCategory: string
  onCategoryChange: (category: string) => void
  categories: Category[]
  currentCustomer: string
  onCustomerChange: (value: string) => void
  prescriptionFile: File | null
  onPrescriptionFileChange: (file: File | null) => void
  discountCode: string
  onDiscountCodeChange: (value: string) => void
}

export default function SearchAndFilters({
  searchTerm,
  onSearchChange,
  selectedCategory,
  onCategoryChange,
  categories,
  currentCustomer,
  onCustomerChange,
  prescriptionFile,
  onPrescriptionFileChange,
  discountCode,
  onDiscountCodeChange
}: SearchAndFiltersProps) {

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    onPrescriptionFileChange(file)
  }

  return (
    <div className="space-y-4">
      {/* Búsqueda, Cliente, Receta y Código de Descuento - TODO EN UNA SOLA CARD */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Búsqueda */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Buscar medicamento o código..."
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 h-12 text-lg"
              />
            </div>

            {/* Cliente */}
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Cliente"
                value={currentCustomer}
                onChange={(e) => onCustomerChange(e.target.value)}
                className="pl-10 h-12 text-lg"
              />
            </div>

            {/* Receta */}
            <div className="relative">
              <input
                type="file"
                accept="image/*,.pdf"
                onChange={handleFileChange}
                className="hidden"
                id="prescription-upload"
              />
              <Button
                variant="outline"
                className="w-full h-12 text-sm justify-start rounded-full"
                onClick={() => document.getElementById('prescription-upload')?.click()}
              >
                <Paperclip className="h-5 w-5 mr-2" />
                {prescriptionFile ? prescriptionFile.name : 'Adjuntar Receta'}
              </Button>
            </div>

            {/* Código de Descuento */}
            <div className="relative">
              <Tag className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Cód. descuento"
                value={discountCode}
                onChange={(e) => onDiscountCodeChange(e.target.value)}
                className="pl-10 h-12 text-lg rounded-full"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filtros de Categorías */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => {
              const IconComponent = category.icon
              const isSelected = selectedCategory === category.name
              
              return (
                <Badge
                  key={category.name}
                  variant={isSelected ? "default" : "outline"}
                  className={`cursor-pointer px-4 py-2 text-sm font-medium transition-all duration-200 rounded-full ${
                    isSelected 
                      ? 'bg-primary text-primary-foreground hover:bg-primary/90' 
                      : 'hover:bg-secondary'
                  }`}
                  onClick={() => onCategoryChange(category.name)}
                >
                  <IconComponent className="h-4 w-4 mr-2" />
                  {category.name}
                </Badge>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
