# Funcionalidad de Impresión y Exportación - Cierre de Caja

## Descripción General

El sistema de cierre de caja incluye múltiples opciones de impresión y exportación para generar reportes detallados de las operaciones diarias de la farmacia.

## Funcionalidades Implementadas

### 1. Exportación a CSV

El sistema permite exportar diferentes tipos de datos en formato CSV:

#### Tipos de Exportación Disponibles:

1. **Cierre de Caja Completo** (`cierre-de-caja-FECHA.csv`)
   - Datos generales del cierre
   - Información del cajero, turno, horarios
   - Totales de ventas, descuentos, devoluciones
   - Número de boletas y facturas

2. **Resumen de Medios de Pago** (`medios-de-pago-FECHA.csv`)
   - Totales por cada método de pago
   - Número de transacciones por método
   - Formato: Medio de Pago, Monto, Transacciones

3. **Arqueo de Caja** (`arqueo-de-caja-FECHA.csv`)
   - Efectivo esperado vs efectivo contado
   - Diferencias calculadas automáticamente
   - Observaciones del arqueo

4. **Detalle de Ventas** (`detalle-ventas-FECHA.csv`)
   - Lista completa de todas las transacciones del día
   - Información detallada de cada venta
   - Incluye número de recibo, cliente, productos, totales

### 2. Impresión de Reportes

#### Vista Previa de Impresión
- Modal de vista previa antes de imprimir
- Formato optimizado para impresión
- Diseño profesional con encabezados y secciones organizadas

#### Características del Reporte Impreso:
- **Encabezado**: Logo y información de la farmacia
- **Datos del Cierre**: Fecha, cajero, turno, horarios
- **Resumen de Ventas**: Totales, boletas, facturas
- **Medios de Pago**: Tabla detallada por método de pago
- **Arqueo de Caja**: Conteo de efectivo y diferencias
- **Detalle de Ventas**: Lista opcional de transacciones
- **Firmas**: Espacios para cajero y supervisor

### 3. Notificaciones de Éxito

Sistema de notificaciones toast que confirma:
- Exportación exitosa de archivos CSV
- Descarga completada
- Errores en caso de problemas

## Uso de las Funcionalidades

### Acceso al Cierre de Caja
1. Hacer clic en el botón "Cierre de Caja" en el header del POS
2. Completar los datos requeridos (cajero, horarios, arqueo)
3. Usar las opciones de exportación e impresión

### Exportar Datos
1. Hacer clic en "Exportar Datos" (menú dropdown)
2. Seleccionar el tipo de exportación deseada
3. El archivo CSV se descargará automáticamente
4. Aparecerá una notificación de confirmación

### Imprimir Reporte
1. Hacer clic en "Imprimir Reporte"
2. Se abrirá la vista previa del reporte
3. Revisar el contenido en la vista previa
4. Hacer clic en "Imprimir" para enviar a la impresora
5. O "Cancelar" para cerrar sin imprimir

## Estructura de Archivos CSV

### Medios de Pago
```csv
Medio de Pago,Monto,Transacciones
Efectivo,600000,15
Débito,400000,8
Crédito,120000,3
Transferencia,50000,2
```

### Cierre de Caja
```csv
Campo,Valor
Fecha,17-06-2025
Cajero,Juan Pérez
Turno,Tarde
Hora de apertura,08:30
Hora de cierre,18:00
Nº total boletas,127
Nº total facturas,4
Total ventas (bruto),1250000
Total descuentos,50000
Total devoluciones,30000
Total ventas neto,1170000
```

### Arqueo de Caja
```csv
Concepto,Monto
Efectivo esperado,600000
Efectivo contado,598000
Diferencia,-2000
Observaciones,Faltante menor, se notificó a supervisión
```

## Componentes Técnicos

### Archivos Principales:
- `CashClosingModal.tsx`: Modal principal del cierre de caja
- `ExportDropdown.tsx`: Menú dropdown para opciones de exportación
- `PrintPreviewModal.tsx`: Vista previa de impresión
- `Toast.tsx`: Sistema de notificaciones
- `utils/index.ts`: Funciones de utilidad para exportación e impresión

### Funciones de Utilidad:
- `generatePaymentSummaryCSV()`: Genera CSV de medios de pago
- `generateDailySalesCSV()`: Genera CSV de datos del cierre
- `generateSalesDetailCSV()`: Genera CSV de detalle de ventas
- `generatePrintableHTML()`: Genera HTML para impresión
- `downloadCSV()`: Maneja la descarga de archivos CSV
- `printHTML()`: Abre ventana de impresión

## Personalización

### Modificar Formato de Reporte
El formato del reporte impreso se puede personalizar editando la función `generatePrintableHTML()` en `utils/index.ts`.

### Agregar Nuevos Tipos de Exportación
1. Crear nueva función de generación CSV en `utils/index.ts`
2. Agregar opción al array `exportOptions` en `CashClosingModal.tsx`
3. Implementar el handler correspondiente

### Cambiar Estilos de Impresión
Los estilos CSS para impresión están incluidos en el HTML generado y se pueden modificar en la función `generatePrintableHTML()`.

## Compatibilidad

- **Navegadores**: Compatible con todos los navegadores modernos
- **Impresoras**: Funciona con cualquier impresora configurada en el sistema
- **Formatos**: CSV para datos tabulares, HTML/PDF para reportes completos
- **Dispositivos**: Optimizado para desktop, funcional en tablets

## Notas Importantes

- Los archivos CSV usan codificación UTF-8 para caracteres especiales
- Las fechas se formatean según el estándar chileno (DD-MM-YYYY)
- Los montos se muestran en pesos chilenos sin decimales
- La vista previa de impresión permite revisar antes de imprimir
- Las notificaciones desaparecen automáticamente después de 3 segundos
